#include "style_manager.h"

/*********************
 *  STATIC VARIABLES
 *********************/
static common_styles_t g_styles;
static bool g_styles_initialized = false;

/*********************
 *  STATIC PROTOTYPES
 *********************/
static void init_container_style(void);
static void init_label_styles(void);
static void init_button_styles(void);

/*********************
 *   GLOBAL FUNCTIONS
 *********************/

void style_manager_init(void)
{
    if (g_styles_initialized) {
        return;
    }

    init_container_style();
    init_label_styles();
    init_button_styles();
    
    g_styles_initialized = true;
}

void style_manager_deinit(void)
{
    if (!g_styles_initialized) {
        return;
    }

    lv_style_reset(&g_styles.container);
    lv_style_reset(&g_styles.label_default);
    lv_style_reset(&g_styles.label_focus);
    lv_style_reset(&g_styles.button_default);
    lv_style_reset(&g_styles.button_focus);
    
    g_styles_initialized = false;
}

common_styles_t* style_manager_get_styles(void)
{
    if (!g_styles_initialized) {
        style_manager_init();
    }
    return &g_styles;
}

lv_obj_t* style_create_container(lv_obj_t* parent)
{
    lv_obj_t* container = lv_obj_create(parent);
    lv_obj_remove_style_all(container);
    lv_obj_add_style(container, &g_styles.container, LV_STATE_DEFAULT);
    lv_obj_set_size(container, CONTAINER_FULL_SIZE);
    lv_obj_set_pos(container, 0, 0);
    return container;
}

lv_obj_t* style_create_label(lv_obj_t* parent, const char* text)
{
    lv_obj_t* label = lv_label_create(parent);
    lv_obj_add_style(label, &g_styles.label_default, LV_STATE_DEFAULT);
    if (text) {
        lv_label_set_text(label, text);
    }
    return label;
}

void style_set_focus(lv_obj_t* obj, bool focused)
{
    if (!obj) return;
    
    // 根据对象类型设置不同的焦点样式
    const lv_obj_class_t* class_p = lv_obj_get_class(obj);
    
    if (class_p == &lv_label_class) {
        lv_obj_remove_style_all(obj);
        if (focused) {
            lv_obj_add_style(obj, &g_styles.label_focus, LV_STATE_DEFAULT);
        } else {
            lv_obj_add_style(obj, &g_styles.label_default, LV_STATE_DEFAULT);
        }
    } else if (class_p == &lv_btn_class) {
        lv_obj_remove_style_all(obj);
        if (focused) {
            lv_obj_add_style(obj, &g_styles.button_focus, LV_STATE_DEFAULT);
        } else {
            lv_obj_add_style(obj, &g_styles.button_default, LV_STATE_DEFAULT);
        }
    }
}

/*********************
 *   STATIC FUNCTIONS
 *********************/

static void init_container_style(void)
{
    lv_style_init(&g_styles.container);
    lv_style_set_bg_color(&g_styles.container, COLOR_BLACK);
    lv_style_set_bg_opa(&g_styles.container, LV_OPA_COVER);
    lv_style_set_border_width(&g_styles.container, 0);
    lv_style_set_pad_all(&g_styles.container, 0);
}

static void init_label_styles(void)
{
    // 默认标签样式
    lv_style_init(&g_styles.label_default);
    lv_style_set_text_color(&g_styles.label_default, COLOR_WHITE);
    lv_style_set_text_opa(&g_styles.label_default, LV_OPA_COVER);
    lv_style_set_bg_opa(&g_styles.label_default, LV_OPA_TRANSP);
    
    // 焦点标签样式
    lv_style_init(&g_styles.label_focus);
    lv_style_set_text_color(&g_styles.label_focus, COLOR_FOCUS);
    lv_style_set_text_opa(&g_styles.label_focus, LV_OPA_COVER);
    lv_style_set_bg_opa(&g_styles.label_focus, LV_OPA_TRANSP);
}

static void init_button_styles(void)
{
    // 默认按钮样式
    lv_style_init(&g_styles.button_default);
    lv_style_set_bg_color(&g_styles.button_default, COLOR_BLACK);
    lv_style_set_bg_opa(&g_styles.button_default, LV_OPA_COVER);
    lv_style_set_border_width(&g_styles.button_default, 1);
    lv_style_set_border_color(&g_styles.button_default, COLOR_WHITE);
    
    // 焦点按钮样式
    lv_style_init(&g_styles.button_focus);
    lv_style_set_bg_color(&g_styles.button_focus, COLOR_FOCUS);
    lv_style_set_bg_opa(&g_styles.button_focus, LV_OPA_COVER);
    lv_style_set_border_width(&g_styles.button_focus, 2);
    lv_style_set_border_color(&g_styles.button_focus, COLOR_WHITE);
}
