#include "page_main.h"
#include "page_menu.h"
#include "rx5808.h"
#include "rx5808_config.h"
#include "lvgl_stl.h"
#include "lv_port_disp.h"
#include "page_start.h"
#include "beep.h"
#include "osd_font.h"
#include "style_manager.h"
LV_FONT_DECLARE(lv_font_montserrat_16);
LV_FONT_DECLARE(lv_font_chinese_12);

#define page_main_anim_enter lv_anim_path_bounce
#define page_main_anim_leave lv_anim_path_bounce

    LV_IMG_DECLARE(lock_img);
LV_IMG_DECLARE(unlock_img);
LV_FONT_DECLARE(lv_font_fre);

static lv_obj_t *main_contain;
static lv_obj_t *lock_btn;
lv_obj_t *lv_channel_label;
static lv_obj_t *frequency_label_contain;
static lv_obj_t *frequency_label[fre_pre_cur_count][fre_label_count];

static lv_obj_t *rssi_bar0;
static lv_obj_t *rssi_label0;

static lv_obj_t *rssi_bar1;
static lv_obj_t *rssi_label1;
static lv_obj_t *lv_rsss0_label;
static lv_obj_t *lv_rsss1_label;

static lv_obj_t *rssi_chart;
static lv_chart_series_t *rssi_curve;

static lv_timer_t *page_main_update_timer;

bool lock_flag = false; // lock
bool menu_flag = false; // menu

static void page_main_exit(void);
static void page_main_group_create(void);
// static void fre_label_update(uint8_t a, uint8_t b);

lv_timer_t *page_mian_time;
lv_indev_state_t key_state = LV_INDEV_STATE_RELEASED;

// 定时器回调函数
static void page_mian_time_callback(lv_timer_t *tmr)
{
    if (tmr == page_mian_time)
    {
        // 在此处执行返回主界面的操作  如果这个函数里面有另一个回调函数说明这是两个页面返回的时间
        lock_flag = false;
        menu_flag = false;
        page_main_exit();
        page_main_create();            // 主界面
        lv_timer_del(page_mian_time);  // 删除定时器
        video_composite_switch(false); // 取消同步
    }
    // 重置用户交互标志
    key_state = LV_INDEV_STATE_RELEASED;
}

// 检测按键状态
void key_delay_time(uint32_t time_out) // 10000 = 10s
{
    // 创建定时器
    page_mian_time = lv_timer_create(page_mian_time_callback, time_out, NULL);
    // 手动获取按键状态
    lv_indev_t *indev = lv_indev_get_act(); // 获取当前处理的设备
    if (indev)
    {
        key_state = lv_indev_get_key(indev); // 获取按键状态

        if (key_state == LV_INDEV_STATE_PRESSED)
        {
            // 按键被按下
            // 重置定时器触发次数，重新开始计时  1次
            lv_timer_set_repeat_count(page_mian_time, 1);
        }
    }
}

static void show_osd_tips(void)
{
    if (lock_flag == false)
        return;

    clear_operation_tip_area(0, 0, 10, 3); // 320 * 96

    if (RX5808_Get_Language() == 0)
    {
        draw_string(10, 8, "UP AND DOWN BANDS");
        draw_string(10, 25, "LEFT AND RIGHT CHANNELS");

        draw_string(10, 41, "LONG PRESS THE LOCK screen");
        draw_string(10, 57, "SHORT PRESS ENTER MENU");
    }
    else
    {
        // 上下调频道
        draw_hanzi(10, 10, 1);
        draw_hanzi(26, 10, 2);
        draw_hanzi(42, 10, 12);
        draw_hanzi(58, 10, 14);
        draw_hanzi(74, 10, 15);

        // 左右调通道
        draw_hanzi(10, 30, 3);
        draw_hanzi(26, 30, 4);
        draw_hanzi(42, 30, 12);
        draw_hanzi(58, 30, 13);
        draw_hanzi(74, 30, 15);

        // 长按锁屏
        draw_hanzi(172, 10, 9);
        draw_hanzi(188, 10, 11);
        draw_hanzi(204, 10, 25);
        draw_hanzi(220, 10, 26);

        // 短按进入菜单
        draw_hanzi(156, 30, 10);
        draw_hanzi(172, 30, 11);
        draw_hanzi(188, 30, 21);
        draw_hanzi(204, 30, 22);
        draw_hanzi(220, 30, 23);
        draw_hanzi(236, 30, 24);
    }
}

static void event_callback(lv_event_t *event)
{
    lv_event_code_t code = lv_event_get_code(event);
    if (code == LV_EVENT_KEY)
    {
        if (lock_flag == true)
        {
            lv_key_t key_status = lv_indev_get_key(lv_indev_get_act());
            if ((key_status >= LV_KEY_UP && key_status <= LV_KEY_LEFT))
            {
                beep_on_off(beep_get_status());
                lv_fun_param_delayed(beep_on_off, 100, 0);
            }
            if (key_status == LV_KEY_LEFT)
            {
                channel_count++;
                if (channel_count > 7)
                    channel_count = 0;
                RX5808_Set_Freq(Rx5808_Freq[Chx_count][channel_count]);
                Rx5808_Set_Channel(channel_count + Chx_count * 8);
                rx5808_div_setup_upload(rx5808_div_config_channel);
                fre_label_update(Chx_count, channel_count);
                lv_label_set_text_fmt(lv_channel_label, "%c%d", Rx5808_ChxMap[Chx_count], channel_count + 1);
                key_delay_time(return_delay_time);
            }
            else if (key_status == LV_KEY_RIGHT)
            {
                channel_count--;
                if (channel_count < 0)
                    channel_count = 7;
                RX5808_Set_Freq(Rx5808_Freq[Chx_count][channel_count]);
                Rx5808_Set_Channel(channel_count + Chx_count * 8);
                rx5808_div_setup_upload(rx5808_div_config_channel);
                fre_label_update(Chx_count, channel_count);
                lv_label_set_text_fmt(lv_channel_label, "%c%d", Rx5808_ChxMap[Chx_count], channel_count + 1);
                key_delay_time(return_delay_time);
            }
            else if (key_status == LV_KEY_UP)
            {
                Chx_count--;
                if (Chx_count < 0)
                    Chx_count = (Rx5808_ChxMap_sum - 1);
                RX5808_Set_Freq(Rx5808_Freq[Chx_count][channel_count]);
                Rx5808_Set_Channel(channel_count + Chx_count * 8);
                rx5808_div_setup_upload(rx5808_div_config_channel);
                fre_label_update(Chx_count, channel_count);
                lv_label_set_text_fmt(lv_channel_label, "%c%d", Rx5808_ChxMap[Chx_count], channel_count + 1);
                key_delay_time(return_delay_time);
            }
            else if (key_status == LV_KEY_DOWN)
            {
                Chx_count++;
                if (Chx_count > (Rx5808_ChxMap_sum - 1))
                    Chx_count = 0;
                RX5808_Set_Freq(Rx5808_Freq[Chx_count][channel_count]);
                Rx5808_Set_Channel(channel_count + Chx_count * 8);
                rx5808_div_setup_upload(rx5808_div_config_channel);
                fre_label_update(Chx_count, channel_count);
                lv_label_set_text_fmt(lv_channel_label, "%c%d", Rx5808_ChxMap[Chx_count], channel_count + 1);
                key_delay_time(return_delay_time);
            }
        }
    }
    else if (code == LV_EVENT_SHORT_CLICKED)
    {
        if (lock_flag == true)
        {
            beep_on_off(beep_get_status());
            lv_fun_param_delayed(beep_on_off, 100, 0);

            // lv_timer_pause(page_mian_time); //  暂停
            lv_timer_reset(page_mian_time); //  重置
            lv_timer_del(page_mian_time);   //  删除
            // key_delay_time_menu( return_delay_time );
            page_main_exit();
            lv_fun_param_delayed(page_menu_create, 500, 0);
        }
    }
    else if (code == LV_EVENT_LONG_PRESSED)
    {
        beep_on_off(beep_get_status());
        lv_fun_param_delayed(beep_on_off, 100, 0);
        if (lock_flag == false)
        {
            // lv_obj_set_style_bg_color(lock_btn, lv_color_make(160, 160, 160), LV_STATE_DEFAULT);  // 灰
            lv_imgbtn_set_src(lock_btn, LV_IMGBTN_STATE_RELEASED, &unlock_img, NULL, NULL);
            menu_flag = true;
            lock_flag = true;
            key_delay_time(return_delay_time);
        }
        else
        {
            // lv_obj_set_style_bg_color(lock_btn, lv_color_make(255, 0, 0), LV_STATE_DEFAULT);   // 红
            lv_imgbtn_set_src(lock_btn, LV_IMGBTN_STATE_RELEASED, &lock_img, NULL, NULL);
            menu_flag = false;
            lock_flag = false;
            lv_timer_reset(page_mian_time); //  重置
            lv_timer_del(page_mian_time);   //  删除
            clear_operation_tip_area(0, 0, 10, 3);
        }
        // 开启或关闭OSD与帧同步
        video_composite_switch(lock_flag);
        video_composite_sync_switch(lock_flag);
        show_osd_tips();
    }
}

static void IRAM_ATTR page_main_update(lv_timer_t *tmr)
{
    // 优化：减少函数调用，缓存信号源
    static uint8_t last_signal_source = 255; // 初始化为无效值
    uint8_t signal_source = RX5808_Get_Signal_Source();

    // 优化：只在信号源改变时更新UI结构
    if (signal_source != last_signal_source) {
        last_signal_source = signal_source;
    }

    // 优化：减少浮点运算，直接使用整数
    int rssi0 = (int)Rx5808_Get_Precentage0();
    int rssi1 = (int)Rx5808_Get_Precentage1();

    if (signal_source == 0)
    {
        lv_bar_set_value(rssi_bar0, rssi1, LV_ANIM_OFF); // 关闭动画提高性能
        lv_label_set_text_fmt(rssi_label0, "%d", rssi1);
        lv_bar_set_value(rssi_bar1, rssi0, LV_ANIM_OFF);
        lv_label_set_text_fmt(rssi_label1, "%d", rssi0);
    }
    else if (signal_source == 1)
    {
        lv_chart_set_next_value(rssi_chart, rssi_curve, rssi1);
        lv_label_set_text_fmt(rssi_label0, "%d", rssi1);
    }
    else
    {
        lv_chart_set_next_value(rssi_chart, rssi_curve, rssi0);
        lv_label_set_text_fmt(rssi_label0, "%d", rssi0);
    }
}

static void fre_pre_label_update()
{

    for (int i = 0; i < fre_label_count; i++)
    {
        lv_label_set_text_fmt(frequency_label[fre_pre][i], "%c", lv_label_get_text(frequency_label[fre_cur][i])[0]);
    }
}

void IRAM_ATTR fre_label_update(uint8_t a, uint8_t b)
{
    // 优化：预计算频率值，避免重复计算
    uint16_t freq = Rx5808_Freq[a][b];

    // 优化：使用查找表代替pow函数
    static const uint16_t pow10_table[4] = {1000, 100, 10, 1};

    for (int i = 0; i < fre_label_count; i++)
    {
        // 优化：避免使用lv_pow，使用预计算的查找表
        int digit = (freq / pow10_table[i]) % 10;
        lv_label_set_text_fmt(frequency_label[fre_cur][i], "%d", digit);

        // 优化：减少字符串比较，直接比较数字
        if (lv_label_get_text(frequency_label[fre_pre][i])[0] != ('0' + digit))
        {
            // 优化：简化动画计算
            int offset = (i % 2) ? -50 : 50;
            lv_amin_start(frequency_label[fre_cur][i], offset, 0, 1, 200, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, lv_anim_path_ease_in);
            lv_amin_start(frequency_label[fre_pre][i], 0, -offset, 1, 200, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, lv_anim_path_ease_in);
        }
    }
    lv_fun_delayed(fre_pre_label_update, 200);
}

// void page_main_rssi_quality_create(uint16_t type)
void page_main_rssi_quality_create()
{
    // if (type == 0)
    // {
    lv_rsss0_label = lv_label_create(main_contain);
    lv_label_set_long_mode(lv_rsss0_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(lv_rsss0_label, LV_ALIGN_TOP_LEFT, 0, 50);
    // lv_obj_set_style_text_font(lv_rsss0_label, &lv_font_montserrat_12, LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(lv_rsss0_label, lv_color_make(0, 0, 255), LV_STATE_DEFAULT);
    lv_label_set_recolor(lv_rsss0_label, true);
    // lv_label_set_text(lv_rsss0_label, "RSSI1:");

    lv_rsss1_label = lv_label_create(main_contain);
    lv_label_set_long_mode(lv_rsss1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(lv_rsss1_label, LV_ALIGN_TOP_LEFT, 0, 65);
    // lv_obj_set_style_text_font(lv_rsss1_label, &lv_font_montserrat_12, LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(lv_rsss1_label, lv_color_make(200, 0, 200), LV_STATE_DEFAULT);
    lv_label_set_recolor(lv_rsss1_label, true);
    // lv_label_set_text(lv_rsss1_label, "RSSI2:");

    if (RX5808_Get_Language() == 0)
    {
        lv_obj_set_style_text_font(lv_rsss0_label, &lv_font_montserrat_12, LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(lv_rsss1_label, &lv_font_montserrat_12, LV_STATE_DEFAULT);
        lv_label_set_text(lv_rsss0_label, "RSSI1:");
        lv_label_set_text(lv_rsss1_label, "RSSI2:");
    }
    else
    {
        lv_obj_set_style_text_font(lv_rsss0_label, &lv_font_chinese_12, LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(lv_rsss1_label, &lv_font_chinese_12, LV_STATE_DEFAULT);
        lv_label_set_text(lv_rsss0_label, "信号1:");
        lv_obj_align(lv_rsss0_label, LV_ALIGN_TOP_LEFT, 3, 50);
        lv_label_set_text(lv_rsss1_label, "信号2:");
        lv_obj_align(lv_rsss1_label, LV_ALIGN_TOP_LEFT, 3, 65);
    }
    rssi_bar0 = lv_bar_create(main_contain);
    lv_obj_remove_style(rssi_bar0, NULL, LV_PART_KNOB);
    lv_obj_set_size(rssi_bar0, 100, 12);
    // 二值化OSD优化
    lv_obj_set_style_bg_color(rssi_bar0, lv_color_black(), LV_PART_MAIN);
    lv_obj_set_style_border_color(rssi_bar0, lv_color_make(50, 50, 80), LV_PART_MAIN);
    lv_obj_set_style_border_width(rssi_bar0, 1, LV_PART_MAIN);
    // 二值化OSD优化 end
    lv_obj_set_style_bg_color(rssi_bar0, lv_color_make(0, 0, 200), LV_PART_INDICATOR);
    lv_obj_set_pos(rssi_bar0, 40, 50);
    lv_bar_set_value(rssi_bar0, Rx5808_Get_Precentage1(), LV_ANIM_ON);
    lv_obj_set_style_anim_time(rssi_bar0, 200, LV_STATE_DEFAULT);

    rssi_label0 = lv_label_create(main_contain);
    lv_obj_align_to(rssi_label0, rssi_bar0, LV_ALIGN_OUT_RIGHT_MID, 5, 0);
    lv_obj_set_style_bg_opa(rssi_label0, (lv_opa_t)LV_OPA_COVER, LV_STATE_DEFAULT);
    lv_obj_set_style_radius(rssi_label0, 4, LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(rssi_label0, lv_color_black(), LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(rssi_label0, &lv_font_montserrat_12, LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(rssi_label0, lv_color_white(), LV_STATE_DEFAULT);
    lv_label_set_recolor(rssi_label0, true);
    lv_label_set_text_fmt(rssi_label0, "%d", (int)Rx5808_Get_Precentage1());

    rssi_bar1 = lv_bar_create(main_contain);
    lv_obj_remove_style(rssi_bar1, NULL, LV_PART_KNOB);
    lv_obj_set_size(rssi_bar1, 100, 12);
    // 二值化OSD优化
    lv_obj_set_style_bg_color(rssi_bar1, lv_color_black(), LV_PART_MAIN);
    lv_obj_set_style_border_width(rssi_bar1, 1, LV_PART_MAIN);
    lv_obj_set_style_border_color(rssi_bar1, lv_color_make(50, 50, 80), LV_PART_MAIN);
    // 二值化OSD优化 end
    lv_obj_set_style_bg_color(rssi_bar1, lv_color_make(200, 0, 200), LV_PART_INDICATOR);
    lv_obj_set_pos(rssi_bar1, 40, 65);
    lv_bar_set_value(rssi_bar1, Rx5808_Get_Precentage0(), LV_ANIM_ON);
    lv_obj_set_style_anim_time(rssi_bar1, 200, LV_STATE_DEFAULT);

    rssi_label1 = lv_label_create(main_contain);
    lv_obj_align_to(rssi_label1, rssi_bar1, LV_ALIGN_OUT_RIGHT_MID, 5, 0);
    lv_obj_set_style_bg_opa(rssi_label1, (lv_opa_t)LV_OPA_COVER, LV_STATE_DEFAULT);
    lv_obj_set_style_radius(rssi_label1, 4, LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(rssi_label1, lv_color_black(), LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(rssi_label1, &lv_font_montserrat_12, LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(rssi_label1, lv_color_white(), LV_STATE_DEFAULT);
    lv_label_set_recolor(rssi_label1, true);
    lv_label_set_text_fmt(rssi_label1, "%d", (int)Rx5808_Get_Precentage0());

    lv_amin_start(lv_rsss0_label, 80, 50, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(rssi_label0, 80, 50, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(rssi_bar0, 80, 51, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(lv_rsss1_label, 95, 65, 1, 500, 100, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(rssi_label1, 95, 65, 1, 500, 100, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(rssi_bar1, 95, 66, 1, 500, 100, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
}

void page_main_create()
{
    show_osd_tips();

    // 初始化样式管理器
    style_manager_init();

    // 使用样式管理器创建主容器
    main_contain = style_create_container(lv_scr_act());

    // 创建频率标签容器
    frequency_label_contain = lv_obj_create(main_contain);
    lv_obj_remove_style_all(frequency_label_contain);
    lv_obj_add_style(frequency_label_contain, &style_manager_get_styles()->container, LV_STATE_DEFAULT);
    lv_obj_set_size(frequency_label_contain, 130, 50);
    lv_obj_set_pos(frequency_label_contain, 30, 0);

    // 5865
    for (int i = 0; i < fre_label_count; i++)
    {
        frequency_label[fre_pre][i] = lv_label_create(frequency_label_contain);
        lv_label_set_long_mode(frequency_label[fre_pre][i], LV_LABEL_LONG_WRAP);
        lv_obj_set_pos(frequency_label[fre_pre][i], 32 * i, -50);
        lv_obj_set_style_text_font(frequency_label[fre_pre][i], &lv_font_fre, LV_STATE_DEFAULT);
        lv_obj_set_style_text_color(frequency_label[fre_pre][i], lv_color_make(255, 157, 0), LV_STATE_DEFAULT);
        lv_label_set_text_fmt(frequency_label[fre_pre][i], "%d", (int)((Rx5808_Freq[Chx_count][channel_count] / lv_pow(10, fre_label_count - i - 1)) % 10));
        lv_label_set_long_mode(frequency_label[fre_pre][i], LV_LABEL_LONG_WRAP);
    }

    for (int i = 0; i < fre_label_count; i++)
    {
        frequency_label[fre_cur][i] = lv_label_create(frequency_label_contain);
        lv_label_set_long_mode(frequency_label[fre_cur][i], LV_LABEL_LONG_WRAP);
        lv_obj_set_pos(frequency_label[fre_cur][i], 32 * i, 0);
        lv_obj_set_style_text_font(frequency_label[fre_cur][i], &lv_font_fre, LV_STATE_DEFAULT);
        lv_obj_set_style_text_color(frequency_label[fre_cur][i], lv_color_make(255, 157, 0), LV_STATE_DEFAULT);
        lv_label_set_text_fmt(frequency_label[fre_cur][i], "%d", (int)((Rx5808_Freq[Chx_count][channel_count] / lv_pow(10, fre_label_count - i - 1)) % 10));
        lv_label_set_long_mode(frequency_label[fre_cur][i], LV_LABEL_LONG_WRAP);
    }

    // A1
    lv_channel_label = lv_label_create(main_contain);
    lv_label_set_long_mode(lv_channel_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(lv_channel_label, LV_ALIGN_TOP_LEFT, 5, 2);
    lv_obj_set_style_text_font(lv_channel_label, &lv_font_montserrat_16, LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(lv_channel_label, lv_color_make(0, 157, 255), LV_STATE_DEFAULT);
    lv_label_set_recolor(lv_channel_label, true);
    lv_label_set_text_fmt(lv_channel_label, "%c%d", Rx5808_ChxMap[Chx_count], channel_count + 1);

    // page_main_rssi_quality_create(RX5808_Get_Signal_Source());
    page_main_rssi_quality_create();

    lock_btn = lv_imgbtn_create(main_contain); // 图片 锁
    lv_obj_remove_style(lock_btn, NULL, LV_PART_ANY);
    lv_obj_set_size(lock_btn, 22, 24);
    lv_obj_set_pos(lock_btn, 6, 23); // 相对于底板 偏移量
    lv_obj_set_style_bg_opa(lock_btn, LV_OPA_COVER, 0);
    lv_obj_set_style_width(lock_btn, 22, LV_STATE_PRESSED);
    lv_obj_set_style_height(lock_btn, 24, LV_STATE_PRESSED);

    if (lock_flag == false)
        // lv_obj_set_style_bg_color(lock_btn, lv_color_hex(0xff0000), LV_STATE_DEFAULT);  // 图片锁定颜色  红
        lv_imgbtn_set_src(lock_btn, LV_IMGBTN_STATE_RELEASED, &lock_img, NULL, NULL);
    else
    {
        // lv_obj_set_style_bg_color(lock_btn, lv_color_hex(0x999999), LV_STATE_DEFAULT);  // 图片解锁颜色  灰
        lv_imgbtn_set_src(lock_btn, LV_IMGBTN_STATE_PRESSED, &unlock_img, NULL, NULL);
        key_delay_time(return_delay_time);
    }
    lv_obj_set_style_bg_color(lock_btn, lv_color_hex(0x000000), LV_STATE_DEFAULT);
    // lv_obj_set_style_border_color(lock_btn, lv_color_make(128, 128, 128), LV_STATE_DEFAULT);  // 边框颜色
    // lv_obj_set_style_border_opa(lock_btn, 0, LV_STATE_DEFAULT);    // 边框透明度
    // lv_obj_set_style_radius(lock_btn, 4, 0);   // 边框角度

    if (menu_flag == true)
        lv_imgbtn_set_src(lock_btn, LV_IMGBTN_STATE_RELEASED, &unlock_img, NULL, NULL);
    else
        lv_imgbtn_set_src(lock_btn, LV_IMGBTN_STATE_RELEASED, &lock_img, NULL, NULL);

    if (RX5808_Get_Signal_Source() == 0)
        page_main_update_timer = lv_timer_create(page_main_update, 200, NULL);  // Faster update for better responsiveness
    else
        page_main_update_timer = lv_timer_create(page_main_update, 50, NULL);   // Faster chart update

    lv_amin_start(lv_channel_label, -48, 2, 1, 800, 200, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(lock_btn, -27, 23, 1, 800, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(frequency_label[fre_cur][0], -50, 0, 1, 800, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(frequency_label[fre_cur][1], -50, 0, 1, 800, 200, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(frequency_label[fre_cur][2], -50, 0, 1, 800, 400, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    lv_amin_start(frequency_label[fre_cur][3], -50, 0, 1, 800, 600, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);

    lv_fun_delayed(page_main_group_create, 500);
}

static void page_main_group_create()
{
    lv_obj_add_event_cb(lock_btn, event_callback, LV_EVENT_ALL, NULL);
    lv_group_t *group = lv_group_create();
    lv_indev_set_group(indev_keypad, group);
    lv_group_add_obj(group, lock_btn);
    lv_group_set_editing(group, false);
}

static void page_main_exit()
{
    lv_obj_remove_event_cb(lock_btn, event_callback);
    lv_timer_del(page_main_update_timer);

    lv_amin_start(lv_channel_label, lv_obj_get_y(lv_channel_label), -48, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
    lv_amin_start(lock_btn, lv_obj_get_y(lock_btn), -27, 1, 800, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
    lv_amin_start(frequency_label[fre_cur][0], lv_obj_get_y(frequency_label[fre_cur][0]), -50, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
    lv_amin_start(frequency_label[fre_cur][1], lv_obj_get_y(frequency_label[fre_cur][1]), -50, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
    lv_amin_start(frequency_label[fre_cur][2], lv_obj_get_y(frequency_label[fre_cur][2]), -50, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
    lv_amin_start(frequency_label[fre_cur][3], lv_obj_get_y(frequency_label[fre_cur][3]), -50, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);

    if (RX5808_Get_Signal_Source() == 0)
    {
        lv_amin_start(lv_rsss0_label, lv_obj_get_y(lv_rsss0_label), 80, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
        lv_amin_start(rssi_label0, lv_obj_get_y(rssi_label0), 80, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
        lv_amin_start(rssi_bar0, lv_obj_get_y(rssi_bar0), 80, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
        lv_amin_start(lv_rsss1_label, lv_obj_get_y(lv_rsss1_label), 95, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
        lv_amin_start(rssi_label1, lv_obj_get_y(rssi_label1), 95, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
        lv_amin_start(rssi_bar1, lv_obj_get_y(rssi_bar1), 95, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_leave);
    }
    else
    {
        lv_amin_start(lv_rsss0_label, lv_obj_get_y(lv_rsss0_label), 80, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
        lv_amin_start(rssi_label0, lv_obj_get_y(rssi_label0), 80, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
        lv_amin_start(rssi_chart, lv_obj_get_y(rssi_chart), 80, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_main_anim_enter);
    }

    lv_obj_del_delayed(main_contain, 500);
}
