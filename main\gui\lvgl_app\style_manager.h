#ifndef __STYLE_MANAGER_H
#define __STYLE_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "../lvgl/lvgl.h"

/*********************
 *      DEFINES
 *********************/
// 通用颜色定义
#define COLOR_BLACK         lv_color_make(0, 0, 0)
#define COLOR_WHITE         lv_color_make(255, 255, 255)
#define COLOR_FOCUS         lv_color_make(255, 100, 0)
#define COLOR_BAR           lv_color_make(255, 168, 0)
#define COLOR_SWITCH        lv_color_make(255, 0, 128)

// 通用尺寸定义
#define SCREEN_WIDTH        160
#define SCREEN_HEIGHT       80
#define CONTAINER_FULL_SIZE SCREEN_WIDTH, SCREEN_HEIGHT

/*********************
 *      TYPEDEFS
 *********************/
typedef struct {
    lv_style_t container;
    lv_style_t label_default;
    lv_style_t label_focus;
    lv_style_t button_default;
    lv_style_t button_focus;
} common_styles_t;

/*********************
 * GLOBAL PROTOTYPES
 *********************/

/**
 * 初始化通用样式
 */
void style_manager_init(void);

/**
 * 释放通用样式
 */
void style_manager_deinit(void);

/**
 * 获取通用样式实例
 * @return 通用样式指针
 */
common_styles_t* style_manager_get_styles(void);

/**
 * 创建标准容器
 * @param parent 父对象
 * @return 创建的容器对象
 */
lv_obj_t* style_create_container(lv_obj_t* parent);

/**
 * 创建标准标签
 * @param parent 父对象
 * @param text 标签文本
 * @return 创建的标签对象
 */
lv_obj_t* style_create_label(lv_obj_t* parent, const char* text);

/**
 * 设置对象焦点样式
 * @param obj 目标对象
 * @param focused 是否获得焦点
 */
void style_set_focus(lv_obj_t* obj, bool focused);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*__STYLE_MANAGER_H*/
