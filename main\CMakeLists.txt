# 精简的LVGL源文件收集 - 仅包含必要组件
file(GLOB SOURCES_CORE
    "./main.c"
    "./hardware/*.c"
    "./driver/*.c"
    "./gui/lvgl_driver/*.c"
    "./gui/lvgl_app/*.c"
    "./gui/lvgl_app/resources/*.c"
)

# LVGL核心组件 - 仅包含必要部分
file(GLOB SOURCES_LVGL_CORE
    "./gui/lvgl/src/core/*.c"
    "./gui/lvgl/src/hal/*.c"
    "./gui/lvgl/src/misc/*.c"
    "./gui/lvgl/src/draw/*.c"     # 核心绘制函数
    "./gui/lvgl/src/draw/sw/*.c"  # 软件渲染
)

# LVGL基础组件 - 仅包含使用的widget
file(GLOB SOURCES_LVGL_WIDGETS
    "./gui/lvgl/src/widgets/lv_label.c"
    "./gui/lvgl/src/widgets/lv_img.c"
    "./gui/lvgl/src/widgets/lv_btn.c"
    "./gui/lvgl/src/widgets/lv_bar.c"
    "./gui/lvgl/src/widgets/lv_table.c"
)

# LVGL额外组件 - 仅包含必要的
file(GLOB SOURCES_LVGL_EXTRA
    "./gui/lvgl/src/extra/lv_extra.c"
    "./gui/lvgl/src/extra/layouts/flex/*.c"
    "./gui/lvgl/src/extra/themes/default/*.c"
    "./gui/lvgl/src/extra/widgets/chart/*.c"
    "./gui/lvgl/src/extra/widgets/imgbtn/*.c"
)

# LVGL字体 - 仅包含必要字体
file(GLOB SOURCES_LVGL_FONTS
    "./gui/lvgl/src/font/lv_font.c"
    "./gui/lvgl/src/font/lv_font_fmt_txt.c"
    "./gui/lvgl/src/font/lv_font_montserrat_12.c"
)

# 合并所有源文件
set(SOURCES
    ${SOURCES_CORE}
    ${SOURCES_LVGL_CORE}
    ${SOURCES_LVGL_WIDGETS}
    ${SOURCES_LVGL_EXTRA}
    ${SOURCES_LVGL_FONTS}
)
# 精简的包含目录 - 仅包含必要路径
set(INCLUDE_DIRSx
    "./"
    "./hardware/"
    "./driver/"
    "./gui/"
    "./gui/lvgl/"
    "./gui/lvgl/src/"
    "./gui/lvgl/src/core/"
    "./gui/lvgl/src/draw/"
    "./gui/lvgl/src/draw/sw/"
    "./gui/lvgl/src/extra/"
    "./gui/lvgl/src/extra/layouts/flex/"
    "./gui/lvgl/src/extra/themes/default/"
    "./gui/lvgl/src/extra/widgets/chart/"
    "./gui/lvgl/src/extra/widgets/imgbtn/"
    "./gui/lvgl/src/font/"
    "./gui/lvgl/src/hal/"
    "./gui/lvgl/src/misc/"
    "./gui/lvgl/src/widgets/"
    "./gui/lvgl_app/"
    "./gui/lvgl_app/resources/"
    "./gui/lvgl_driver/"
)

set(SRCSx
    main.c
    hardware/msp.c
)

idf_component_register(SRCS ${SRCSx} ${SOURCES} INCLUDE_DIRS ${INCLUDE_DIRSx})

# ESP32 Performance Optimizations
target_compile_options(${COMPONENT_LIB} PRIVATE
    -ffast-math
    -funroll-loops
    -finline-functions
    -fno-stack-protector  # Disable stack protection for performance
    -Os  # Optimize for size
)

# Link-time optimization for further size reduction
target_link_options(${COMPONENT_LIB} PRIVATE
    -flto
    -Wl,--gc-sections  # Remove unused sections
)

# idf_component_register(SRCS "main.c" INCLUDE_DIRS "")
