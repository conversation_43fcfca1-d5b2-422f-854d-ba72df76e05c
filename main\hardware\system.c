#include <stdint.h>
#include "driver/gpio.h"
#include "24cxx.h"
#include "lcd.h"
#include "rx5808.h"
#include "rx5808_config.h"
#include "system.h"
#include "esp_timer.h"
#include "lv_port_disp.h"
#include "lv_port_indev.h"
#include "hwvers.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "beep.h"
#include "Backpack.h"

void create_cpu_stack_monitor_task();
void cpu_stack_monitor_task(void *param);



void LED_Init()
{

	gpio_set_direction(LED1_Pin_Num, GPIO_MODE_OUTPUT);
	gpio_set_level(LED1_Pin_Num, 0);


}

esp_timer_handle_t esp_timer_tick = 0;
void timer_periodic_cb(void *arg)
{
	lv_tick_inc(1);
}

esp_timer_create_args_t periodic_arg = {.callback =
											&timer_periodic_cb,
										.arg = NULL,
										.name = "LVGL_TICK_TIMER"};

void timer_init()
{
	esp_err_t err;
	err = esp_timer_create(&periodic_arg, &esp_timer_tick); // 创建定时器
	ESP_ERROR_CHECK(err);
	err = esp_timer_start_periodic(esp_timer_tick, 1 * 1000); // 启动定时器 周期为1s
	ESP_ERROR_CHECK(err);
	//   printf("Timer_Init! OK\n");
}

void system_init(void)
{
	LCD_Init();
	LED_Init();
	eeprom_24cxx_init();
	rx5808_div_setup_load();
	Beep_Init();
	timer_init();
	RX5808_Init();
	// Backpack初始化
	Backpack_init();
}

/*
make menuconfig -> Component config -> FreeRTOS -> Enable FreeRTOS trace facility
make menuconfig -> Component config -> FreeRTOS -> Enable FreeRTOS trace facility -> Enable FreeRTOS stats formatting functions
make menuconfig -> Component config -> FreeRTOS -> Enable FreeRTOS to collect run time stats*/
void create_cpu_stack_monitor_task()
{

	// 双核CPU调用 - Further optimized stack size
	xTaskCreatePinnedToCore((TaskFunction_t)cpu_stack_monitor_task,
							"CPU_STACK",
							1024, 
							NULL,
							1,
							NULL,
							0); // 至关重要，决定在那个核上运行
}

void cpu_stack_monitor_task(void *param)
{

	while (1)
	{

		vTaskDelay(10 / portTICK_PERIOD_MS);
	}
}
