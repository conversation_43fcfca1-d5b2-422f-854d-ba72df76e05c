#include "rx5808.h"
#include "driver/gpio.h"
#include "driver/adc.h"
#include "esp_adc_cal.h"
#include "esp_log.h"
#include "unistd.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "hwvers.h"

#include <string.h>
#include <stdio.h>

#define Synthesizer_Register_A 0x00
#define Synthesizer_Register_B 0x01
#define Synthesizer_Register_C 0x02
#define Synthesizer_Register_D 0x03
#define VCO_Switch_Cap_Control_Register 0x04
#define DFC_Control_Register 0x05
#define _6M_Audio_Demodulator_Control_Register 0x06
#define _6M5_Audio_Demodulator_Control_Register 0x07
#define Receiver_control_Register_1 0x08
#define Receiver_control_Register_2 0x09
#define Power_Down_Control_Register 0x0A
#define State_Register 0x0F

// adc1_channel_t adc_dma_chan[]={RX5808_RSSI0_CHAN,RX5808_RSSI1_CHAN,VBAT_ADC_CHAN,KEY_ADC_CHAN};

// 彻底关断接收机
bool RX5808_Shutdown = false;
// uint16_t adc_convert_temp0[32][3];
// uint16_t adc_convert_temp1[32][3];
//  uint16_t adc_converted_value[3]={4096,4096,1024};
uint16_t adc_converted_value[3] = {1024, 1024, 1024};

volatile int8_t Chx_count = 0;	   // 频率数组中的 频道
volatile int8_t channel_count = 0; // 频率数组中的 频率

volatile uint8_t Rx5808_channel;
volatile uint16_t Rx5808_RSSI_Ad_Min0 = 0;
volatile uint16_t Rx5808_RSSI_Ad_Max0 = 4095;
volatile uint16_t Rx5808_RSSI_Ad_Min1 = 0;
volatile uint16_t Rx5808_RSSI_Ad_Max1 = 4095;
volatile uint16_t Rx5808_OSD_Format = 0;
volatile uint16_t Rx5808_Language = 1;
volatile uint16_t Rx5808_Beep_Format = 1;
volatile uint16_t Rx5808_Signal_Source = 0;

// RX5808 8个频道 接收频率 MHz 引脚电平由CH1~3 控制
const char DRAM_ATTR Rx5808_ChxMap[Rx5808_ChxMap_sum] = {'A', 'B', 'E', 'F', 'R', 'H', 'L', 'U', 'O', 'X'};
const uint16_t DRAM_ATTR Rx5808_Freq[Rx5808_ChxMap_sum][8] =
	{
		{5865, 5845, 5825, 5805, 5785, 5765, 5745, 5725}, // A	CH1-8
		{5733, 5752, 5771, 5790, 5809, 5828, 5847, 5866}, // B	CH1-8
		{5705, 5685, 5665, 5645, 5885, 5905, 5925, 5945}, // E	CH1-8
		{5740, 5760, 5780, 5800, 5820, 5840, 5860, 5880}, // F	CH1-8
		{5658, 5695, 5732, 5769, 5806, 5843, 5880, 5917}, // R	CH1-8
		{5653, 5693, 5733, 5773, 5813, 5853, 5893, 5933}, // H	CH1-8
		{5333, 5373, 5413, 5453, 5493, 5533, 5573, 5613}, // L	CH1-8
		{5325, 5348, 5366, 5384, 5402, 5420, 5438, 5456}, // U	CH1-8
		{5474, 5492, 5510, 5528, 5546, 5564, 5582, 5600}, // O	CH1-8
		{4990, 5020, 5050, 5080, 5110, 5140, 5170, 5200}, // X	CH1-8
		// {6002,6013,6028,6030,6054,6080,6106,6132},		//Z 6G

};

static esp_adc_cal_characteristics_t adc1_chars;

#define ADC_RESULT_BYTE 2
#define ADC_CONV_LIMIT_EN 1					 // For ESP32, this should always be set to 1
#define ADC_CONV_MODE ADC_CONV_SINGLE_UNIT_1 // ESP32 only supports ADC1 DMA mode
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE1

// ADC 接受信号强度初始化   RSSI 接受信号强度指示
void RX5808_RSSI_ADC_Init()
{
	esp_err_t ret;
	ret = esp_adc_cal_check_efuse(ESP_ADC_CAL_VAL_EFUSE_VREF);
	if (ret == ESP_OK)
	{
		esp_adc_cal_characterize(ADC_UNIT_1, ADC_ATTEN_DB_11, ADC_WIDTH_BIT_DEFAULT, 0, &adc1_chars);
	}
	else
	{
		printf("adc calib failed!\n");
	}
	adc_set_clk_div(1);
	// 通常，位宽越高，ADC 的分辨率越高，能够提供更精确的模拟信号转换。但是，高分辨率也可能带来更多的噪声和更长的转换时间。12位位宽
	adc1_config_width(ADC_WIDTH_BIT_12);						   // 配置ADC使用内部参考电压（1.1V） ???? 通常是1.1V
	adc1_config_channel_atten(RX5808_RSSI0_CHAN, ADC_ATTEN_DB_11); // 选择ADC的通道
	adc1_config_channel_atten(RX5808_RSSI1_CHAN, ADC_ATTEN_DB_11);
	adc1_config_channel_atten(VBAT_ADC_CHAN, ADC_ATTEN_DB_11); // 电池电压
	adc1_config_channel_atten(KEY_ADC_CHAN, ADC_ATTEN_DB_11);


}

void RX5808_Init()
{

	gpio_set_direction(RX5808_SCLK, GPIO_MODE_OUTPUT);
	gpio_set_direction(RX5808_MOSI, GPIO_MODE_OUTPUT);
	gpio_set_direction(RX5808_CS, GPIO_MODE_OUTPUT);
	gpio_reset_pin(RX5808_SWITCH0); // added it on 9/5,analogue switch;
	gpio_set_direction(RX5808_SWITCH0, GPIO_MODE_OUTPUT);
	gpio_reset_pin(RX5808_SWITCH1);
	gpio_set_direction(RX5808_SWITCH1, GPIO_MODE_OUTPUT);

	gpio_set_level(RX5808_SWITCH0, 1);
	gpio_set_level(RX5808_SWITCH1, 1); // fix it on 9/5,analogue switch;

	Send_Register_Data(Synthesizer_Register_A, 0x00008);

	Send_Register_Data(Power_Down_Control_Register, 0x10DF3);

	RX5808_Set_Freq(Rx5808_Freq[Chx_count][channel_count]);

	RX5808_RSSI_ADC_Init();



	// 双核CPU调用 - Further optimized stack size
	xTaskCreatePinnedToCore((TaskFunction_t)DMA2_Stream0_IRQHandler,
							"rx5808_task",
							1024, // Further reduced for memory optimization
							NULL,
							2,
							NULL,
							1); // 至关重要，决定在那个核上运行
}

void RX5808_Pause()
{
	RX5808_Shutdown = true;
	gpio_set_level(RX5808_SWITCH0, 0);
	gpio_set_level(RX5808_SWITCH1, 0);
}
void RX5808_Resume()
{
	RX5808_Shutdown = false;
	gpio_set_level(RX5808_SWITCH0, 1);
	gpio_set_level(RX5808_SWITCH1, 1); // fix it on 9/5,analogue switch;
}
void IRAM_ATTR Soft_SPI_Send_One_Bit(uint8_t bit)
{
	gpio_set_level(RX5808_SCLK, 0);
	usleep(20);
	gpio_set_level(RX5808_MOSI, ((bit & 0x01) == 1));
	usleep(30);
	gpio_set_level(RX5808_SCLK, 1);
	usleep(20);
}

void IRAM_ATTR Send_Register_Data(uint8_t addr, uint32_t data)
{
	gpio_set_level(RX5808_CS, 0);
	uint8_t read_write = 1; // 1 write     0 read

	for (uint8_t i = 0; i < 4; i++)
		Soft_SPI_Send_One_Bit(((addr >> i) & 0x01));
	Soft_SPI_Send_One_Bit(read_write & 0x01);
	for (uint8_t i = 0; i < 20; i++)
		Soft_SPI_Send_One_Bit(((data >> i) & 0x01));

	gpio_set_level(RX5808_CS, 1);
	gpio_set_level(RX5808_SCLK, 0);
	gpio_set_level(RX5808_MOSI, 0);
}

void IRAM_ATTR RX5808_Set_Freq(uint16_t Fre)
{
	// 优化：预计算常量，减少运算
	const uint16_t F_LO = (Fre - 479) >> 1;

	// 优化：使用位运算代替除法和取模
	const uint16_t N = F_LO >> 5;  // F_LO / 32
	const uint16_t A = F_LO & 0x1F; // F_LO % 32

	// 优化：合并位运算
	Send_Register_Data(Synthesizer_Register_B, (N << 7) | A);
}

void IRAM_ATTR Rx5808_Set_Channel(uint8_t ch)
{
	// 优化：快速边界检查
	if (ch >= Rx5808_Freq_sum) return;

	// 优化：使用位运算代替除法和取模
	Rx5808_channel = ch;
	Chx_count = ch >> 3;        // ch / 8
	channel_count = ch & 0x07;  // ch % 8
}

void RX5808_Set_RSSI_Ad_Min0(uint16_t value)
{
	Rx5808_RSSI_Ad_Min0 = value;
}

void RX5808_Set_RSSI_Ad_Max0(uint16_t value)
{
	Rx5808_RSSI_Ad_Max0 = value;
}
void RX5808_Set_RSSI_Ad_Min1(uint16_t value)
{
	Rx5808_RSSI_Ad_Min1 = value;
}
void RX5808_Set_RSSI_Ad_Max1(uint16_t value)
{
	Rx5808_RSSI_Ad_Max1 = value;
}

void RX5808_Set_OSD_Format(uint16_t value)
{
	Rx5808_OSD_Format = value;
}

void RX5808_Set_Language(uint16_t value)
{
	Rx5808_Language = value;
}

void RX5808_Set_Signal_Source(uint16_t value)
{
	Rx5808_Signal_Source = value;
}

uint16_t Rx5808_Get_Channel()
{
	return Rx5808_channel;
}
uint16_t RX5808_Get_RSSI_Ad_Min0()
{
	return Rx5808_RSSI_Ad_Min0;
}
uint16_t RX5808_Get_RSSI_Ad_Max0()
{
	return Rx5808_RSSI_Ad_Max0;
}
uint16_t RX5808_Get_RSSI_Ad_Min1()
{
	return Rx5808_RSSI_Ad_Min1;
}
uint16_t RX5808_Get_RSSI_Ad_Max1()
{
	return Rx5808_RSSI_Ad_Max1;
}
uint16_t RX5808_Get_OSD_Format()
{
	return Rx5808_OSD_Format;
}
uint16_t RX5808_Get_Language()
{
	return Rx5808_Language;
}
uint16_t RX5808_Get_Signal_Source()
{
	return Rx5808_Signal_Source;
}
float Rx5808_Get_Precentage0_value()
{
	return adc_converted_value[0];
}

float Rx5808_Get_Precentage1_value()
{
	return adc_converted_value[1];
}

// 校准接受信号强度
bool RX5808_Calib_RSSI(uint16_t min0, uint16_t max0, uint16_t min1, uint16_t max1)
{
	// if((min0>=max0)||(min1>=max1))
	// return false;

	if ((min0 + RX5808_CALIB_RSSI_ADC_VALUE_THRESHOULD <= max0) && (min1 + RX5808_CALIB_RSSI_ADC_VALUE_THRESHOULD <= max1))
		return true;

	return false;
}

#if 0
// 假设 ADC 满量程读数为 4095（12 位 ADC），参考电压为 3.3V
#define ADC_MAX 4095
#define V_REF 3.3

// 已知电压范围  0.7-1.0 正常
#define MIN_VOLTAGE 0.80
#define MAX_VOLTAGE 1.15

// 计算电压对应的 ADC 读数范围
#define MIN_ADC_VALUE ((uint32_t)((MIN_VOLTAGE / V_REF) * ADC_MAX))
#define MAX_ADC_VALUE ((uint32_t)((MAX_VOLTAGE / V_REF) * ADC_MAX))

#elif 0

#define RSSI0_MAX_SPAN 1250 // 信号2
#define RSSI0_MIN_SPAN 750	// 0.73 - 1.16
#define RSSI1_MAX_SPAN 1280 // 信号1
#define RSSI1_MIN_SPAN 810	// 0.77 - 1.19
// max 不变  min 减小  值增大
// max 不变  min 增大  值减小
// max 减小  min 不变  值增大
// max 增大  min 不变  值减小

#elif 1
#define RSSI_CHANNELS 2

// 定义每个通道的最小和最大阈值
static const struct
{
	uint16_t min;
	uint16_t max;
} RSSI_THRESHOLDS[RSSI_CHANNELS] = {
	{.min = 750, .max = 1245}, // 通道0
	{.min = 810, .max = 1275}  // 通道1
};
// 最大返回百分比值
#define MAX_RSSI_PERCENT 99.0f
#endif

// 计算接受信号强度百分比 - 高度优化版本
float IRAM_ATTR Rx5808_Calculate_RSSI_Precentage(uint16_t value, uint8_t channel)
{
	// 快速边界检查 - 单次比较
	if (channel >= RSSI_CHANNELS) return 0.0f;

	// 使用局部变量减少内存访问
	const uint16_t min = RSSI_THRESHOLDS[channel].min;
	const uint16_t max = RSSI_THRESHOLDS[channel].max;

	// 优化的边界检查 - 减少分支
	if (value <= min) return 0.0f;
	if (value >= max) return MAX_RSSI_PERCENT;

	// 高性能整数运算 - 避免浮点除法
	const uint16_t range = max - min;
	const uint16_t offset = value - min;

	// 使用位移优化乘法 (offset * 100 = offset * 64 + offset * 32 + offset * 4)
	const uint32_t scaled = (offset << 6) + (offset << 5) + (offset << 2);

	// 单次除法转换为浮点
	return (float)scaled / range;
}

float Rx5808_Get_Precentage0()
{
	return Rx5808_Calculate_RSSI_Precentage(adc_converted_value[0], 0);
}

float Rx5808_Get_Precentage1()
{
	return Rx5808_Calculate_RSSI_Precentage(adc_converted_value[1], 1);
}

// 分压系数，根据实际分压电路调整
// 使用硬件配置中定义的分压比例
#define DIVIDER_RATIO VBAT_RATE
// 电池电压
float Get_Battery_Voltage()
{

	// 校准 ADC 参考电压
	esp_adc_cal_characteristics_t adc_chars;
	esp_adc_cal_characterize(ADC_UNIT_1, ADC_ATTEN_DB_11, ADC_WIDTH_BIT_12, 3300, &adc_chars);

	// 将 ADC 读数转换为电压（mV）
	uint32_t voltage_mV = esp_adc_cal_raw_to_voltage(adc_converted_value[2], &adc_chars);

	// 考虑分压电路，计算实际电池电压（mV）
	float battery_voltage_mV = voltage_mV * DIVIDER_RATIO;

	// 将电压转换为伏特（V）
	float battery_voltage_V = battery_voltage_mV / 1000.0;

	return battery_voltage_V;
}

// 直接访问ADC原始读数 - 内联优化
float Get_original_Voltage0()
{
	return adc1_get_raw(RX5808_RSSI0_CHAN);
}
float Get_original_Voltage1()
{
	return adc1_get_raw(RX5808_RSSI1_CHAN);
}



// DMA 数据流中断   使能频道 x 开关
void DMA2_Stream0_IRQHandler(void)
{

	static uint8_t rx5808_cur_receiver_best = rx5808_receiver_count;
	static uint8_t rx5808_pre_receiver_best = rx5808_receiver_count;
	while (1)
	{
		// 优化：减少局部变量，直接累加到全局数组
		uint32_t sum0 = 0, sum1 = 0;

		// 优化：展开循环减少分支开销
		sum0 += adc1_get_raw(RX5808_RSSI0_CHAN);
		sum1 += adc1_get_raw(RX5808_RSSI1_CHAN);
		sum0 += adc1_get_raw(RX5808_RSSI0_CHAN);
		sum1 += adc1_get_raw(RX5808_RSSI1_CHAN);
		sum0 += adc1_get_raw(RX5808_RSSI0_CHAN);
		sum1 += adc1_get_raw(RX5808_RSSI1_CHAN);
		sum0 += adc1_get_raw(RX5808_RSSI0_CHAN);
		sum1 += adc1_get_raw(RX5808_RSSI1_CHAN);

		// 优化：使用位移代替除法
		adc_converted_value[0] = sum0 >> 2;  // 4次采样平均
		adc_converted_value[1] = sum1 >> 2;
		adc_converted_value[2] = adc1_get_raw(VBAT_ADC_CHAN);

		// Rx5808_Signal_Source 获取到的值{ "自动","接收机1","接收机2","关闭"}; 0:自动  1：接收机1  2：接收机2  3：关闭
		int sig_src = Rx5808_Signal_Source;
		// 关断则都为0
		if (RX5808_Shutdown)
		{
			sig_src = 3;
		}
		if (sig_src == 1)
		{
			gpio_set_level(RX5808_SWITCH1, 1);
			gpio_set_level(RX5808_SWITCH0, 0);
		}
		else if (sig_src == 2)
		{
			gpio_set_level(RX5808_SWITCH1, 0);
			gpio_set_level(RX5808_SWITCH0, 1);
		}
		else if (sig_src == 3)
		{
			gpio_set_level(RX5808_SWITCH0, 0);
			gpio_set_level(RX5808_SWITCH1, 0);
		}
		else
		{
			// 优化：获取RSSI值
			const float rssi0 = Rx5808_Get_Precentage0();
			const float rssi1 = Rx5808_Get_Precentage1();

			// 优化：使用绝对值函数减少分支
			const float rssi_diff = (rssi0 > rssi1) ? (rssi0 - rssi1) : (rssi1 - rssi0);

			if (rssi_diff >= RX5808_TOGGLE_DEAD_ZONE) // RX5808_TOGGLE_DEAD_ZONE = 1
			{
				// 优化：确定最佳接收机
				const uint8_t new_best = (rssi0 > rssi1) ? rx5808_receiver0 : rx5808_receiver1;

				if (new_best == rx5808_pre_receiver_best)
				{
					// 优化：合并GPIO设置逻辑
					const bool use_rx0 = (new_best == rx5808_receiver0);
					gpio_set_level(RX5808_SWITCH0, use_rx0 ? 1 : 0);
					gpio_set_level(RX5808_SWITCH1, use_rx0 ? 0 : 1);
				}
				rx5808_cur_receiver_best = new_best;
				rx5808_pre_receiver_best = new_best;
			}
		}

		vTaskDelay(5 / portTICK_PERIOD_MS);
	}
}
