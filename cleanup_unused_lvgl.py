#!/usr/bin/env python3
"""
LVGL未使用文件清理脚本
删除项目中未使用的LVGL组件文件以减少代码体积
"""

import os
import shutil
import sys

# 定义要删除的未使用LVGL组件目录
UNUSED_DIRS = [
    # GPU相关目录 - 已禁用所有GPU支持
    "main/gui/lvgl/src/gpu",
    "main/gui/lvgl/src/draw/nxp_pxp",
    "main/gui/lvgl/src/draw/nxp_vglite", 
    "main/gui/lvgl/src/draw/sdl",
    "main/gui/lvgl/src/draw/stm32_dma2d",
    
    # 未使用的主题
    "main/gui/lvgl/src/extra/themes/basic",
    "main/gui/lvgl/src/extra/themes/mono",
    
    # 未使用的布局
    "main/gui/lvgl/src/extra/layouts/grid",
    
    # 未使用的额外组件
    "main/gui/lvgl/src/extra/widgets/animimg",
    "main/gui/lvgl/src/extra/widgets/calendar", 
    "main/gui/lvgl/src/extra/widgets/colorwheel",
    "main/gui/lvgl/src/extra/widgets/keyboard",
    "main/gui/lvgl/src/extra/widgets/led",
    "main/gui/lvgl/src/extra/widgets/list",
    "main/gui/lvgl/src/extra/widgets/menu",
    "main/gui/lvgl/src/extra/widgets/meter",
    "main/gui/lvgl/src/extra/widgets/msgbox",
    "main/gui/lvgl/src/extra/widgets/span",
    "main/gui/lvgl/src/extra/widgets/spinbox",
    "main/gui/lvgl/src/extra/widgets/spinner",
    "main/gui/lvgl/src/extra/widgets/tabview",
    "main/gui/lvgl/src/extra/widgets/tileview",
    "main/gui/lvgl/src/extra/widgets/win",
    
    # 未使用的第三方库
    "main/gui/lvgl/src/extra/libs/bmp",
    "main/gui/lvgl/src/extra/libs/ffmpeg",
    "main/gui/lvgl/src/extra/libs/freetype",
    "main/gui/lvgl/src/extra/libs/fsdrv",
    "main/gui/lvgl/src/extra/libs/gif",
    "main/gui/lvgl/src/extra/libs/png",
    "main/gui/lvgl/src/extra/libs/qrcode",
    "main/gui/lvgl/src/extra/libs/rlottie",
    "main/gui/lvgl/src/extra/libs/sjpg",
    
    # 其他未使用组件
    "main/gui/lvgl/src/extra/others/gridnav",
    "main/gui/lvgl/src/extra/others/monkey",
    "main/gui/lvgl/src/extra/others/snapshot",
]

# 定义要删除的未使用字体文件
UNUSED_FONT_FILES = [
    "main/gui/lvgl/src/font/lv_font_montserrat_8.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_10.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_14.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_16.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_18.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_20.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_22.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_24.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_26.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_28.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_30.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_32.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_34.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_36.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_38.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_40.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_42.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_44.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_46.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_48.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_12_subpx.c",
    "main/gui/lvgl/src/font/lv_font_montserrat_28_compressed.c",
    "main/gui/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c",
    "main/gui/lvgl/src/font/lv_font_simsun_16_cjk.c",
    "main/gui/lvgl/src/font/lv_font_unscii_8.c",
    "main/gui/lvgl/src/font/lv_font_unscii_16.c",
    "main/gui/lvgl/src/font/korean.ttf",
]

# 定义要删除的未使用widget文件
UNUSED_WIDGET_FILES = [
    "main/gui/lvgl/src/widgets/lv_arc.c",
    "main/gui/lvgl/src/widgets/lv_arc.h",
    "main/gui/lvgl/src/widgets/lv_btnmatrix.c",
    "main/gui/lvgl/src/widgets/lv_btnmatrix.h",
    "main/gui/lvgl/src/widgets/lv_canvas.c",
    "main/gui/lvgl/src/widgets/lv_canvas.h",
    "main/gui/lvgl/src/widgets/lv_checkbox.c",
    "main/gui/lvgl/src/widgets/lv_checkbox.h",
    "main/gui/lvgl/src/widgets/lv_dropdown.c",
    "main/gui/lvgl/src/widgets/lv_dropdown.h",
    "main/gui/lvgl/src/widgets/lv_line.c",
    "main/gui/lvgl/src/widgets/lv_line.h",
    "main/gui/lvgl/src/widgets/lv_roller.c",
    "main/gui/lvgl/src/widgets/lv_roller.h",
    "main/gui/lvgl/src/widgets/lv_slider.c",
    "main/gui/lvgl/src/widgets/lv_slider.h",
    "main/gui/lvgl/src/widgets/lv_switch.c",
    "main/gui/lvgl/src/widgets/lv_switch.h",
    "main/gui/lvgl/src/widgets/lv_textarea.c",
    "main/gui/lvgl/src/widgets/lv_textarea.h",
    "main/gui/lvgl/src/widgets/lv_objx_templ.c",
    "main/gui/lvgl/src/widgets/lv_objx_templ.h",
]

def safe_remove_dir(dir_path):
    """安全删除目录"""
    if os.path.exists(dir_path):
        try:
            shutil.rmtree(dir_path)
            print(f"✅ 已删除目录: {dir_path}")
            return True
        except Exception as e:
            print(f"❌ 删除目录失败 {dir_path}: {e}")
            return False
    else:
        print(f"⚠️  目录不存在: {dir_path}")
        return False

def safe_remove_file(file_path):
    """安全删除文件"""
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            print(f"✅ 已删除文件: {file_path}")
            return True
        except Exception as e:
            print(f"❌ 删除文件失败 {file_path}: {e}")
            return False
    else:
        print(f"⚠️  文件不存在: {file_path}")
        return False

def main():
    """主函数"""
    print("🚀 开始清理未使用的LVGL组件...")
    
    total_removed = 0
    
    # 删除未使用的目录
    print("\n📁 删除未使用的目录:")
    for dir_path in UNUSED_DIRS:
        if safe_remove_dir(dir_path):
            total_removed += 1
    
    # 删除未使用的字体文件
    print("\n🔤 删除未使用的字体文件:")
    for file_path in UNUSED_FONT_FILES:
        if safe_remove_file(file_path):
            total_removed += 1
    
    # 删除未使用的widget文件
    print("\n🧩 删除未使用的widget文件:")
    for file_path in UNUSED_WIDGET_FILES:
        if safe_remove_file(file_path):
            total_removed += 1
    
    print(f"\n🎉 清理完成! 总共删除了 {total_removed} 个文件/目录")
    print("💡 建议重新编译项目以验证所有功能正常")

if __name__ == "__main__":
    main()
